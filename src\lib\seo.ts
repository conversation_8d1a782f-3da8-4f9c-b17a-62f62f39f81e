import { Metadata } from 'next'
import { Locale, siteConfig } from '@/config/global'
import { i18nConfig } from '@/config/global'
import { LOCALES } from '@/config/global'
import { trimEnd } from 'lodash'
import { headers } from 'next/headers'

interface PageSEOProps {
  title: string
  description: string
  pathname: string
  locale: Locale
  availableLocales?: Locale[]
  type?: 'article' | 'website'
  image?: string
  [key: string]: any
}

export function genLocalePathname(pathname: string, locale: Locale) {
  if (!pathname.startsWith('/')) {
    pathname = `/${pathname}`
  }
  if (locale != i18nConfig.defaultLocale) {
    pathname = trimEnd(`/${locale}${pathname}`, '/')
  }
  return pathname
}

const SUPPORTED_I18N_ARTICLES = [
  '/article/privacy-policy',
  '/article/terms-of-service',
  '/article/how-do-i-save-videos-on-my-iphone-ipad-or-ipod',
]
export function getOtherLanguagePathname(pathname: string): string {
  if (pathname.startsWith('/article/') && !SUPPORTED_I18N_ARTICLES.includes(pathname)) {
    // if is not supported i18n articels, redirect to home
    return '/'
  } else if (pathname.startsWith('/articles/')) {
    // strip page number
    return pathname.split('/').slice(0, 3).join('/')
  }
  return pathname
}

/**
 * 获取基础URL
 * 从请求头中提取主机信息，并返回以https协议为前缀的完整URL。
 * 注意：部分CDN默认不支持x-forwarded-proto，因此协议固定为https。
 *
 * @returns 包含协议和主机的完整URL字符串
 */
export const getBaseUrl = async (): Promise<string> => {
  const headersList = await headers()
  const host = headersList.get('x-forwarded-host') || headersList.get('host')
  return `https://${host}` // 协议指定为https (部分CDN默认不支持x-forwarded-proto)
}

export function genPageMetadata({
  title,
  description,
  pathname,
  locale,
  availableLocales,
  type,
  image,
  ...rest
}: PageSEOProps): Metadata {
  const currentPathname = genLocalePathname(pathname, locale)
  // Generate alternate links for all available languages
  const locales = availableLocales || LOCALES
  const languages: Record<string, string> = {}
  if (!availableLocales) {
    languages['x-default'] = genLocalePathname(pathname, i18nConfig.defaultLocale)
  }
  locales.forEach((locale) => {
    languages[locale] = genLocalePathname(pathname, locale)
  })

  return {
    title,
    description,
    alternates: {
      canonical: currentPathname,
      languages: languages,
    },
    openGraph: {
      title: title,
      description: description,
      url: currentPathname,
      siteName: siteConfig.name,
      images: image ? [image] : [siteConfig.ogImage],
      type: type || 'website',
    },
    twitter: {
      title: title,
      description: description,
      card: 'summary_large_image',
      images: image ? [image] : [siteConfig.ogImage],
    },
    ...rest,
  }
}