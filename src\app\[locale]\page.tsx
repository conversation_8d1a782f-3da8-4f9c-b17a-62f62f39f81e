import { pick } from 'lodash'
import { NextIntlClientProvider } from 'next-intl'
import { getTranslations, getMessages } from 'next-intl/server'
import { Extractor } from '@/components/client/extractor'
import { notFound } from 'next/navigation'
import { Locale } from '@/config/global'
import type { Metadata } from 'next'
import { genPageMetadata } from '@/lib/seo'
import { defaultSite, FAQ } from '@/config/downloader'
import { siteConfig } from '@/config/global'
import { Link } from '@/navigation'
import { FAQSection } from '@/components/faq-section'
import { Alert, Badge } from 'flowbite-react'
import { AngleRight } from 'flowbite-react-icons/outline'

export const revalidate = 3600 // invalidate every hour

export const defaultHowToUse: Record<Locale, FAQ> = {
  en: {
    question: `How to Use the ${siteConfig.name} Video and Image Downloader Online?`,
    answer: (
      <div>
        <p>
          With the {siteConfig.name} Online Video/Image Downloader, effortlessly capture your favorite videos and images
          from the web without the need for extra software. Experience the convenience of online media downloading
          without any added complications.
        </p>
        <p>
          Whether it's videos, short clips, or sports highlights, {siteConfig.name} makes it easy. Just paste the shared
          URL into the input box above and click the Download button.
        </p>
      </div>
    ),
  },
  zh: {
    question: `如何使用${siteConfig.name}视频图片下载器保存1000+平台视频图片？`,
    answer: (
      <div>
        <p>
          使用 {siteConfig.name}{' '}
          在线视频/图片下载器，无需额外的软件即可轻松从网络保存您喜欢的视频和图片。一键将视频图片保存到手机相册、电脑本地。
        </p>
        <p>
          无论是视频、短片还是体育赛事精彩片段，{siteConfig.name}{' '}
          都能让一切变得简单。只需将复制的媒体分享链接粘贴到上面的输入框，然后点击"提取视频图片"按钮即可。
        </p>
      </div>
    ),
  },
  ja: {
    question: `${siteConfig.name}のオンライン動画・画像ダウンローダーをどのように使用しますか？`,
    answer: (
      <div>
        <p>
          {siteConfig.name}
          のオンライン動画/画像ダウンローダーを使えば、追加ソフトウェア不要で、お気に入りの動画や画像をウェブから簡単にダウンロードできます。オンラインメディアのダウンロードが、これまでにないほど手軽に。
        </p>
        <p>
          ビデオ、ショートクリップ、スポーツのハイライトなど、{siteConfig.name}
          なら簡単にダウンロードできます。共有されたURLを上の入力ボックスに貼り付け、「ダウンロード」ボタンをクリックするだけです。
        </p>
      </div>
    ),
  },
  es: {
    question: `¿Cómo Usar el Descargador de Vídeos e Imágenes en Línea de ${siteConfig.name}?`,
    answer: (
      <div>
        <p>
          Con el Descargador de Vídeos/Imágenes en Línea de {siteConfig.name}, captura fácilmente tus vídeos e imágenes
          favoritos de la web sin necesidad de software adicional. Experimenta la comodidad de descargar medios en línea
          sin complicaciones añadidas.
        </p>
        <p>
          Ya sean vídeos, clips cortos o resúmenes deportivos, {siteConfig.name} lo facilita. Solo pega el URL
          compartido en el cuadro de entrada arriba y haz clic en el botón de Descargar.
        </p>
      </div>
    ),
  },
}

export const defaultFAQ: Record<Locale, FAQ[]> = {
  en: [
    {
      question: 'Where are videos or images saved after being downloaded?',
      answer: `When you're downloading files, they are usually saved into whatever folder you have set as your default. Your browser normally sets this folder for you. In browser settings, you can change and choose manually the destination folder for your downloaded media files.`,
    },
    {
      question: 'How do I save videos to my iPhone (iOS)?',
      answer: (
        <>
          Because of Apple's privacy policy, from iOS 12 and iPadOS 12 and below, you cannot download any videos, music,
          or movies to your iPhone. You should update to the latest iOS version to be able to use Safari to save videos
          from {siteConfig.name}. If you still can't download, please{' '}
          <Link href="/article/how-do-i-save-videos-on-my-iphone-ipad-or-ipod" className="underline font-medium">
            follow the tutorial
          </Link>
          .
        </>
      ),
    },
    {
      question: 'Do I need to install instructions or extensions?',
      answer: `No. I try to keep things easy for our users. All you need are your share links. That's it.`,
    },
  ],
  zh: [
    {
      question: '电脑上部分浏览器点下载视频按钮后，跳转到视频播放页面，如何下载到本地呢？',
      answer: `电脑上少数浏览器不支持直接下载，但可以在下载视频按钮上点击右键，然后选择"目标另存为"或"链接存储为"来下载视频；或者到跳转后的视频页面，在视频画面上点击右键，然后选择"视频另存为"来下载视频。推荐使用Chrome、Firefox、QQ浏览器等主流浏览器。`,
    },
    {
      question: 'Android手机可以下载保存视频吗？',
      answer: `Android手机在常用的Chrome、Firefox、Edge、QQ等浏览器上都可以很方便的使用本站。推荐使用Chrome浏览器获得最佳下载体验。`,
    },
    {
      question: 'iOS设备（iPhone、iPad）上点击下载视频按钮后，跳转到视频页面，并没有直接下载，怎么办？',
      answer: (
        <>
          我们可以用到iOS系统手机自带Safari浏览器点击解析视频后，长按视频下载按钮会弹出一个列表，然后点击下载文件链接，右上角然后把刚下载的视频储存视频到手机相册即可，也可以借助第三方App来完成下载，iOS用户在App
          Store下载免费的Documents ，然后在Documents右下角按钮的内置浏览器中使用本站，可以完美下载视频。
          <Link href="/article/how-do-i-save-videos-on-my-iphone-ipad-or-ipod" className="underline font-medium">
            查看图文操作教程
          </Link>
        </>
      ),
    },
    {
      question: '下载后的文件打不开怎么办？',
      answer: `这种情况出现的比较少。一般是文件后缀问题，如果是视频文件，把下载后的文件后缀名改为".mp4"即可播放；如果是图片，把文件后缀名改为".jpg"即可查看。`,
    },
  ],
  ja: [
    {
      question: 'ダウンロードした動画や画像はどこに保存されますか？',
      answer: `ファイルをダウンロードする際、通常はデフォルト設定されているフォルダに保存されます。ブラウザがこのフォルダを設定しています。ブラウザの設定で、ダウンロードしたメディアファイルの保存先フォルダを手動で変更し、選択することができます。`,
    },
    {
      question: 'iPhone（iOS）に動画を保存する方法は？',
      answer: (
        <>
          Appleのプライバシーポリシーのため、iOS 12およびiPadOS
          12以下では、iPhoneに動画、音楽、映画をダウンロードすることができません。
          {siteConfig.name}
          から動画を保存できるように、最新のiOSバージョンにアップデートする必要があります。それでもダウンロードできない場合は、
          <Link href="/article/how-do-i-save-videos-on-my-iphone-ipad-or-ipod" className="underline font-medium">
            このチュートリアルに従ってください
          </Link>
          。
        </>
      ),
    },
    {
      question: 'インストールが必要な指示や拡張機能はありますか？',
      answer: `いいえ、私たちはユーザーが簡単に利用できるように心がけています。必要なのは共有リンクだけです。それだけです。`,
    },
  ],
  es: [
    {
      question: '¿Dónde se guardan los vídeos o imágenes después de ser descargados?',
      answer:
        'Cuando descargas archivos, normalmente se guardan en la carpeta que hayas establecido como predeterminada. Tu navegador establece esta carpeta por ti. En la configuración del navegador, puedes cambiar y elegir manualmente la carpeta de destino para tus archivos multimedia descargados.',
    },
    {
      question: '¿Cómo guardo vídeos en mi iPhone (iOS)?',
      answer: (
        <>
          Debido a la política de privacidad de Apple, desde iOS 12 e iPadOS 12 y versiones anteriores, no puedes
          descargar ningún vídeo, música o película en tu iPhone. Deberías actualizar a la última versión de iOS para
          poder usar Safari y guardar vídeos desde {siteConfig.name}. Si aún así no puedes descargar, por favor{' '}
          <Link href="/article/how-do-i-save-videos-on-my-iphone-ipad-or-ipod" className="underline font-medium">
            sigue el tutorial
          </Link>
          .
        </>
      ),
    },
    {
      question: '¿Necesito instalar instrucciones o extensiones?',
      answer:
        'No. Intento hacer las cosas fáciles para nuestros usuarios. Todo lo que necesitas son tus enlaces de compartir. Eso es todo.',
    },
  ],
}

export async function generateMetadata({ params }: { params: { locale: Locale } }): Promise<Metadata> {
  const site = defaultSite[params.locale]
  if (!site) {
    notFound()
  }

  return genPageMetadata({
    title: site.title,
    description: site.description,
    pathname: '/',
    locale: params.locale,
    applicationName: site.heading,
  })
}

export default async function Page({ params }: { params: { locale: Locale } }) {
  const site = defaultSite[params.locale]
  const howToUse = defaultHowToUse[params.locale]
  if (!site || !howToUse) {
    notFound()
  }

  const messages = await getMessages()
  const t = await getTranslations('newVersionAlert')

  const faq = [
    {
      question: howToUse.question,
      answer: howToUse.answer,
    },
    ...defaultFAQ[params.locale],
  ]

  return (
    <main className="mb-8">
      <section className="bg-white dark:bg-gray-900 bg-[url('/images/hero-pattern.svg')] dark:bg-[url('/images/hero-pattern-dark.svg')]">
        <div className="bg-gradient-to-b from-blue-50 to-transparent dark:from-blue-900 w-full h-full">
          <div className="container flex flex-col items-center py-12 sm:py-16 gap-4">
            <Link href="/app" className="w-full max-w-4xl flex justify-center">
              <Alert
                color="info"
                className="border-0 bg-transparent cursor-pointer p-0"
                additionalContent={
                  <div className="pl-1 pr-2 py-1 bg-white hover:bg-gray-50 rounded-full inline-flex justify-start items-center gap-2 transition-colors">
                    <Badge className="px-2 py-1 bg-primary-700 rounded-full flex justify-start items-center gap-1">
                      <div className="text-center justify-start text-white text-xs font-medium font-['Inter'] leading-none">New</div>
                    </Badge>
                    <div className="flex justify-start items-center gap-2">
                      <div className="justify-start text-gray-600 text-sm font-normal font-['Inter'] leading-tight">{t('message')}</div>
                      <div className="w-4 h-4 flex items-center justify-center">
                        <AngleRight className="w-4 h-4 text-gray-600" />
                      </div>
                    </div>
                  </div>
                }
              />
            </Link>
            <div className="flex flex-col gap-4 items-center text-center">
              <h1 className="text-gray-900 tracking-tight text-3xl sm:text-4xl lg:text-5xl font-bold">
                {site.heading}
              </h1>
              {site.subheading && <h2 className="text-gray-500 text-lg lg:text-xl">{site.subheading}</h2>}
            </div>
            <NextIntlClientProvider messages={pick(messages, ['extractor.client', 'appDownloadButtons'])}>
              <Extractor />
            </NextIntlClientProvider>
          </div>
        </div>
      </section>
      <FAQSection faq={faq} />
    </main>
  )
}
